site_name: Skaha API
site_url: https://shinybrar.github.io/skaha/
site_author: <PERSON><PERSON>
site_description: Python Client for Skaha Science Platform
repo_url: https://github.com/shinybrar/skaha/
repo_name: shinybrar/skaha
edit_uri: blob/main/docs/
copyright: Copyright &copy; 1986-2024 Canadian Astronomy Data Centre
remote_branch: gh-pages

theme:
  name: material
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/toggle-switch
        name: Hello darkness, my old friend
    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/toggle-switch-off-outline
        name: Flash the neon lights
  font:
    text: Roboto
    code: Roboto Mono
  language: en
  icon:
    repo: fontawesome/brands/github-alt
    edit: material/pencil-box
    view: material/eye-outline
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.instant
    - navigation.instant.progress
    - navigation.prune
    - navigation.sections
    - navigation.tracking
    - navigation.indexes
    - content.code.annotate
    - toc.follow
    - toc.integrate
    - navigation.top

plugins:
  - search
  - mkdocstrings
  - git-revision-date-localized:
        type: date
        fallback_to_build_date: true

extra:
  version:
    provider: mike
    default: latest

# Extensions
markdown_extensions:
  - markdown.extensions.admonition
  - markdown.extensions.attr_list
  - markdown.extensions.def_list
  - markdown.extensions.footnotes
  - markdown.extensions.meta
  - markdown.extensions.toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.critic
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      use_pygments: true
      linenums_style: pymdownx.inline
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: squidfunk
      repo: mkdocs-material
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      check_paths: true
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

nav:
  - Skaha: index.md

  - Get Started: get-started.md
  - Configuration: configuration.md
  - Authentication: authentication.md
  - Examples: examples.md
  - API Reference:
    - AsyncSession: async_session.md
    - Session: session.md
    - Images: images.md
    - Context: context.md
    - Overview: overview.md
    - Client: client.md
  - Change Log: changelog.md
  - Code of Conduct: conduct.md
  - Contributing: contributing.md
  - Bug Reports: bug-reports.md
  - Security: security.md
  - License: license.md
  - OpenSSF Report: https://scorecard.dev/viewer/?uri=github.com/shinybrar/skaha
