"""Tests for HTTPx authentication hooks."""

from __future__ import annotations

import asyncio
import time
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import httpx
import jwt
import pytest
from pydantic import SecretStr

from skaha.client import SkahaClient
from skaha.hooks.httpx.auth import (
    AuthenticationError,
    CertificateExpiredError,
    _refresh_oidc_token_async,
    _refresh_oidc_token_sync,
    _should_skip_refresh,
    _update_client_auth,
    ahook,
    hook,
)
from skaha.models.auth import Authentication, OIDC


class TestShouldSkipRefresh:
    """Test the _should_skip_refresh function."""

    def test_skip_for_user_token(self):
        """Test that refresh is skipped when user provides token."""
        client = Mock(spec=SkahaClient)
        client.token = SecretStr("user-token")
        client.certificate = None
        
        result = _should_skip_refresh(client)
        assert result is True

    def test_skip_for_user_certificate(self):
        """Test that refresh is skipped when user provides certificate."""
        client = Mock(spec=SkahaClient)
        client.token = None
        client.certificate = "/path/to/cert.pem"
        
        result = _should_skip_refresh(client)
        assert result is True

    def test_skip_for_non_oidc_mode_not_expired(self):
        """Test that refresh is skipped for non-OIDC modes when not expired."""
        client = Mock()
        client.token = None
        client.certificate = None
        client.auth = Mock()
        client.auth.mode = "x509"
        client.auth.expired = False

        result = _should_skip_refresh(client)
        assert result is True

    def test_raise_error_for_expired_x509(self):
        """Test that error is raised for expired x509 authentication."""
        client = Mock()
        client.token = None
        client.certificate = None
        client.auth = Mock()
        client.auth.mode = "x509"
        client.auth.expired = True

        with pytest.raises(CertificateExpiredError) as exc_info:
            _should_skip_refresh(client)

        assert "Authentication expired for x509 mode" in str(exc_info.value)
        assert "skaha auth login" in str(exc_info.value)

    def test_skip_for_oidc_not_expired(self):
        """Test that refresh is skipped for OIDC when not expired."""
        client = Mock()
        client.token = None
        client.certificate = None
        client.auth = Mock()
        client.auth.mode = "oidc"
        client.auth.expired = False

        result = _should_skip_refresh(client)
        assert result is True

    def test_proceed_for_oidc_expired(self):
        """Test that refresh proceeds for expired OIDC authentication."""
        client = Mock()
        client.token = None
        client.certificate = None
        client.auth = Mock()
        client.auth.mode = "oidc"
        client.auth.expired = True

        result = _should_skip_refresh(client)
        assert result is False


class TestRefreshOidcTokenSync:
    """Test the _refresh_oidc_token_sync function."""

    @patch('skaha.hooks.httpx.auth.oidc.sync_refresh')
    def test_successful_refresh(self, mock_sync_refresh):
        """Test successful token refresh."""
        mock_token = SecretStr("new-access-token")
        mock_sync_refresh.return_value = mock_token

        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = time.time() + 3600  # Valid for 1 hour
        client.auth.oidc.endpoints = Mock()
        client.auth.oidc.endpoints.token = "https://example.com/token"
        client.auth.oidc.client = Mock()
        client.auth.oidc.client.identity = "client-id"
        client.auth.oidc.client.secret = "client-secret"
        client.auth.oidc.token = Mock()
        client.auth.oidc.token.refresh = "refresh-token"

        result = _refresh_oidc_token_sync(client)

        assert result == mock_token
        mock_sync_refresh.assert_called_once_with(
            url="https://example.com/token",
            identity="client-id",
            secret="client-secret",
            token="refresh-token",
        )

    def test_expired_refresh_token(self):
        """Test error when refresh token is expired."""
        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = time.time() - 3600  # Expired 1 hour ago

        with pytest.raises(AuthenticationError) as exc_info:
            _refresh_oidc_token_sync(client)

        assert "Refresh token expired" in str(exc_info.value)
        assert "skaha auth login" in str(exc_info.value)

    def test_missing_refresh_token_expiry(self):
        """Test error when refresh token expiry is None."""
        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = None

        with pytest.raises(AuthenticationError) as exc_info:
            _refresh_oidc_token_sync(client)

        assert "Refresh token expired" in str(exc_info.value)

    @patch('skaha.hooks.httpx.auth.oidc.sync_refresh')
    def test_refresh_failure(self, mock_sync_refresh):
        """Test handling of refresh failure."""
        mock_sync_refresh.side_effect = Exception("Network error")

        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = time.time() + 3600
        client.auth.oidc.endpoints = Mock()
        client.auth.oidc.endpoints.token = "https://example.com/token"
        client.auth.oidc.client = Mock()
        client.auth.oidc.client.identity = "client-id"
        client.auth.oidc.client.secret = "client-secret"
        client.auth.oidc.token = Mock()
        client.auth.oidc.token.refresh = "refresh-token"

        with pytest.raises(AuthenticationError) as exc_info:
            _refresh_oidc_token_sync(client)

        assert "Failed to refresh OIDC access token" in str(exc_info.value)
        assert "Network error" in str(exc_info.value)


class TestRefreshOidcTokenAsync:
    """Test the _refresh_oidc_token_async function."""

    @patch('skaha.hooks.httpx.auth.oidc.refresh')
    async def test_successful_refresh(self, mock_refresh):
        """Test successful async token refresh."""
        mock_token = SecretStr("new-access-token")
        mock_refresh.return_value = mock_token

        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = time.time() + 3600
        client.auth.oidc.endpoints = Mock()
        client.auth.oidc.endpoints.token = "https://example.com/token"
        client.auth.oidc.client = Mock()
        client.auth.oidc.client.identity = "client-id"
        client.auth.oidc.client.secret = "client-secret"
        client.auth.oidc.token = Mock()
        client.auth.oidc.token.refresh = "refresh-token"

        result = await _refresh_oidc_token_async(client)

        assert result == mock_token
        mock_refresh.assert_called_once_with(
            url="https://example.com/token",
            identity="client-id",
            secret="client-secret",
            token="refresh-token",
        )

    async def test_expired_refresh_token(self):
        """Test error when refresh token is expired."""
        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.expiry = Mock()
        client.auth.oidc.expiry.refresh = time.time() - 3600

        with pytest.raises(AuthenticationError) as exc_info:
            await _refresh_oidc_token_async(client)

        assert "Refresh token expired" in str(exc_info.value)


class TestUpdateClientAuth:
    """Test the _update_client_auth function."""

    @patch('skaha.hooks.httpx.auth.jwt.decode')
    def test_successful_update(self, mock_jwt_decode):
        """Test successful client auth update."""
        mock_jwt_decode.return_value = {"exp": 1234567890}

        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.token = Mock()
        client.auth.oidc.expiry = Mock()
        client.client = Mock()
        client.client.headers = {}

        token = SecretStr("new-access-token")
        request = Mock(spec=httpx.Request)
        request.headers = {}

        _update_client_auth(client, token, request)

        # Verify token was set
        assert client.auth.oidc.token.access == "new-access-token"
        assert client.auth.oidc.expiry.access == 1234567890

        # Verify save was called
        client.save.assert_called_once()

        # Verify headers were updated
        expected_auth = "Bearer new-access-token"
        assert client.client.headers["Authorization"] == expected_auth
        assert request.headers["Authorization"] == expected_auth

    @patch('skaha.hooks.httpx.auth.jwt.decode')
    def test_jwt_decode_failure(self, mock_jwt_decode):
        """Test handling of JWT decode failure."""
        mock_jwt_decode.side_effect = jwt.InvalidTokenError("Invalid token")

        client = Mock()
        client.auth = Mock()
        client.auth.oidc = Mock()
        client.auth.oidc.token = Mock()
        client.auth.oidc.expiry = Mock()
        client.client = Mock()
        client.client.headers = {}

        token = SecretStr("invalid-token")
        request = Mock(spec=httpx.Request)
        request.headers = {}

        # Should not raise exception, just log warning
        _update_client_auth(client, token, request)

        # Token should still be set
        assert client.auth.oidc.token.access == "invalid-token"


class TestSyncHook:
    """Test the synchronous hook function."""

    @patch('skaha.hooks.httpx.auth._should_skip_refresh')
    def test_skip_refresh(self, mock_should_skip):
        """Test that hook skips when _should_skip_refresh returns True."""
        mock_should_skip.return_value = True

        client = Mock()
        hook_func = hook(client)
        request = Mock(spec=httpx.Request)

        # Should not raise any exceptions
        hook_func(request)

        mock_should_skip.assert_called_once_with(client)

    @patch('skaha.hooks.httpx.auth._update_client_auth')
    @patch('skaha.hooks.httpx.auth._refresh_oidc_token_sync')
    @patch('skaha.hooks.httpx.auth._should_skip_refresh')
    def test_successful_refresh(self, mock_should_skip, mock_refresh, mock_update):
        """Test successful token refresh in hook."""
        mock_should_skip.return_value = False
        mock_token = SecretStr("new-token")
        mock_refresh.return_value = mock_token

        client = Mock()
        client.auth = Mock()
        client.auth.expired = True  # First check
        hook_func = hook(client)
        request = Mock(spec=httpx.Request)

        hook_func(request)

        mock_should_skip.assert_called_once_with(client)
        mock_refresh.assert_called_once_with(client)
        mock_update.assert_called_once_with(client, mock_token, request)


class TestAsyncHook:
    """Test the asynchronous hook function."""

    @patch('skaha.hooks.httpx.auth._should_skip_refresh')
    async def test_skip_refresh(self, mock_should_skip):
        """Test that async hook skips when _should_skip_refresh returns True."""
        mock_should_skip.return_value = True

        client = Mock()
        hook_func = ahook(client)
        request = Mock(spec=httpx.Request)

        # Should not raise any exceptions
        await hook_func(request)

        mock_should_skip.assert_called_once_with(client)

    @patch('skaha.hooks.httpx.auth._update_client_auth')
    @patch('skaha.hooks.httpx.auth._refresh_oidc_token_async')
    @patch('skaha.hooks.httpx.auth._should_skip_refresh')
    async def test_successful_refresh(self, mock_should_skip, mock_refresh, mock_update):
        """Test successful token refresh in async hook."""
        mock_should_skip.return_value = False
        mock_token = SecretStr("new-token")
        mock_refresh.return_value = mock_token

        client = Mock()
        client.auth = Mock()
        client.auth.expired = True  # First check
        hook_func = ahook(client)
        request = Mock(spec=httpx.Request)

        await hook_func(request)

        mock_should_skip.assert_called_once_with(client)
        mock_refresh.assert_called_once_with(client)
        mock_update.assert_called_once_with(client, mock_token, request)
