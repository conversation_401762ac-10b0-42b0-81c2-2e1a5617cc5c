"""HTTPx authentication hooks for automatic token refresh and certificate validation.

This module provides httpx event hooks that automatically handle authentication
expiry and refresh for different authentication modes:

- **OIDC Mode**: Automatically refreshes access tokens using refresh tokens
- **X509/Default Mode**: Validates certificate expiry and instructs re-authentication
- **User-provided credentials**: Bypasses automatic refresh

The hooks are designed to be used with httpx clients to provide seamless
authentication management without requiring manual intervention.

Usage:
    The hooks are automatically integrated with SkahaClient instances and do not
    require manual setup. They are applied based on the authentication mode:

    ```python
    from skaha.client import SkahaClient

    # OIDC mode - automatic token refresh
    client = SkahaClient()  # Uses auth.mode = "oidc"
    response = client.client.get("/session")  # Auto-refreshes if needed

    # User-provided token - no automatic refresh
    client = SkahaClient(token=SecretStr("user-token"))
    response = client.client.get("/session")  # Uses provided token
    ```

Note:
    The hooks modify the request before it's sent, updating headers and
    authentication credentials as needed. They also save updated configuration
    to disk when credentials are refreshed.
"""

from __future__ import annotations

import asyncio
import threading
import time
from typing import TYPE_CHECKING, Callable

import jwt

from skaha import get_logger
from skaha.auth import oidc

if TYPE_CHECKING:
    from collections.abc import Awaitable

    import httpx
    from pydantic import SecretStr

    from skaha.client import SkahaClient

log = get_logger(__name__)

# Concurrency protection for token refresh
_sync_refresh_lock = threading.Lock()
_async_refresh_lock = asyncio.Lock()


class AuthenticationError(Exception):
    """Exception raised when authentication refresh fails."""


class CertificateExpiredError(AuthenticationError):
    """Exception raised when X509 certificate is expired."""


def _should_skip_refresh(client: SkahaClient) -> bool:
    """Check if authentication refresh should be skipped.

    Args:
        client (SkahaClient): The SkahaClient instance.

    Returns:
        bool: True if refresh should be skipped, False otherwise.
    """
    # Skip refresh for user-provided credentials
    if client.token or client.certificate:
        log.debug("User-provided credentials detected, skipping auto-refresh")
        return True

    # Skip if not OIDC mode
    if client.auth.mode != "oidc":
        log.debug(
            "Non-OIDC auth mode (%s), checking certificate expiry", client.auth.mode
        )
        # For x509/default modes, check if auth is expired and raise clear error
        if client.auth.expired:
            msg = (
                f"Authentication expired for {client.auth.mode} mode. "
                "Run `skaha auth login` to re-authenticate"
            )
            raise CertificateExpiredError(msg)
        return True

    # Skip if not expired
    if not client.auth.expired:
        log.debug("OIDC authentication not expired, skipping refresh")
        return True

    return False


def _refresh_oidc_token_sync(client: SkahaClient) -> SecretStr:
    """Core synchronous OIDC token refresh logic.

    Args:
        client (SkahaClient): The SkahaClient instance.

    Returns:
        SecretStr: New access token.

    Raises:
        AuthenticationError: If refresh fails.
    """
    # Check refresh token expiry
    if (
        not client.auth.oidc.expiry.refresh
        or client.auth.oidc.expiry.refresh < time.time()
    ):
        log.debug("Refresh token expired, cannot refresh")
        msg = "Refresh token expired. Run `skaha auth login` to re-authenticate"
        raise AuthenticationError(msg)

    try:
        log.debug("Starting OIDC synchronous token refresh")
        token: SecretStr = oidc.sync_refresh(
            url=str(client.auth.oidc.endpoints.token),
            identity=str(client.auth.oidc.client.identity),
            secret=str(client.auth.oidc.client.secret),
            token=str(client.auth.oidc.token.refresh),
        )
        log.debug("OIDC synchronous token refresh successful")
    except Exception as err:
        msg = f"Failed to refresh OIDC access token: {err}"
        log.exception(msg)
        raise AuthenticationError(msg) from err
    else:
        return token


async def _refresh_oidc_token_async(client: SkahaClient) -> SecretStr:
    """Core asynchronous OIDC token refresh logic.

    Args:
        client (SkahaClient): The SkahaClient instance.

    Returns:
        SecretStr: New access token.

    Raises:
        AuthenticationError: If refresh fails.
    """
    # Check refresh token expiry
    if (
        not client.auth.oidc.expiry.refresh
        or client.auth.oidc.expiry.refresh < time.time()
    ):
        log.debug("Refresh token expired, cannot refresh")
        msg = "Refresh token expired. Run `skaha auth login` to re-authenticate"
        raise AuthenticationError(msg)

    try:
        log.debug("Starting OIDC asynchronous token refresh")
        token: SecretStr = await oidc.refresh(
            url=str(client.auth.oidc.endpoints.token),
            identity=str(client.auth.oidc.client.identity),
            secret=str(client.auth.oidc.client.secret),
            token=str(client.auth.oidc.token.refresh),
        )
        log.debug("OIDC asynchronous token refresh successful")
    except Exception as err:
        msg = f"Failed to refresh OIDC access token: {err}"
        log.exception(msg)
        raise AuthenticationError(msg) from err
    else:
        return token


def _update_client_auth(
    client: SkahaClient, token: SecretStr, request: httpx.Request
) -> None:
    """Update client and request with new authentication token.

    Args:
        client (SkahaClient): The SkahaClient instance.
        token (SecretStr): New access token.
        request (httpx.Request): The HTTP request to update.
    """
    try:
        # Update client auth configuration
        client.auth.oidc.token.access = token.get_secret_value()

        # Decode JWT to get expiry (with error handling)
        try:
            decoded = jwt.decode(  # type: ignore [attr-defined]
                str(token), options={"verify_signature": False}
            )
            client.auth.oidc.expiry.access = decoded.get("exp")
        except (jwt.InvalidTokenError, KeyError) as err:  # type: ignore [attr-defined]
            log.warning("Failed to decode JWT for expiry: %s", err)
            # Continue without setting expiry - token is still valid

        # Save configuration
        client.save()
        log.debug("Authentication configuration saved")

        # Update client headers
        auth_header = f"Bearer {client.auth.oidc.token.access}"
        client.client.headers.update({"Authorization": auth_header})
        request.headers.update({"Authorization": auth_header})

        log.debug("Client and request headers updated with new authentication")
        log.info("OIDC authentication token refreshed successfully")

    except Exception as err:
        msg = f"Failed to update client authentication: {err}"
        log.exception(msg)
        raise AuthenticationError(msg) from err


def hook(client: SkahaClient) -> Callable[[httpx.Request], None]:
    """Create a synchronous authentication refresh hook for httpx clients.

    This hook automatically handles OIDC token refresh and validates certificate
    expiry for other authentication modes. It includes concurrency protection
    and user credential bypass logic.

    Args:
        client (SkahaClient): The SkahaClient instance containing auth configuration.

    Returns:
        Callable[[httpx.Request], None]: The synchronous auth hook function.
    """

    def refresh(request: httpx.Request) -> None:
        """Synchronous refresh hook for httpx clients.

        Args:
            request (httpx.Request): The outgoing HTTP request.

        Raises:
            AuthenticationError: If authentication refresh fails.
            CertificateExpiredError: If certificate is expired.
        """
        # Check if refresh should be skipped
        if _should_skip_refresh(client):
            return

        # Use lock to prevent concurrent refresh attempts
        with _sync_refresh_lock:
            # Double-check expiry after acquiring lock
            if not client.auth.expired:
                log.debug("Authentication refreshed by another thread, skipping")
                return

            # Refresh OIDC token
            token = _refresh_oidc_token_sync(client)
            _update_client_auth(client, token, request)

    return refresh


def ahook(client: SkahaClient) -> Callable[[httpx.Request], Awaitable[None]]:
    """Create an asynchronous authentication refresh hook for httpx clients.

    This hook automatically handles OIDC token refresh and validates certificate
    expiry for other authentication modes. It includes concurrency protection
    and user credential bypass logic.

    Args:
        client (SkahaClient): The SkahaClient instance containing auth configuration.

    Returns:
        Callable[[httpx.Request], Awaitable[None]]: The asynchronous auth hook function.
    """

    async def refresh(request: httpx.Request) -> None:
        """Asynchronous refresh hook for httpx clients.

        Args:
            request (httpx.Request): The outgoing HTTP request.

        Raises:
            AuthenticationError: If authentication refresh fails.
            CertificateExpiredError: If certificate is expired.
        """
        # Check if refresh should be skipped
        if _should_skip_refresh(client):
            return

        # Use lock to prevent concurrent refresh attempts
        async with _async_refresh_lock:
            # Double-check expiry after acquiring lock
            if not client.auth.expired:
                log.debug("Authentication refreshed by another coroutine, skipping")
                return

            # Refresh OIDC token
            token = await _refresh_oidc_token_async(client)
            _update_client_auth(client, token, request)

    return refresh
