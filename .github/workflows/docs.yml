name: Deploy Docs

on:
  repository_dispatch:
    types: [docs-publish]

env:
  RELEASES_CREATED: ${{ github.event.client_payload.releases_created }}
  SHA: ${{ github.event.client_payload.sha }}
  TAG: ${{ github.event.client_payload.tag_name }}

permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0080882f6c36860b6ba35c610c98ce87d4e2f26f # v2.10.2
        with:
          egress-policy: audit
      - name: Set up git user
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0
        with:
          fetch-depth: 0
      - name: Client Payload
        run: |
          echo "Client Payload: ${{ toJson(github.event.client_payload) }}"
      - uses: astral-sh/setup-uv@caf0cab7a618c569241d31dcd442f54681755d39 # v3.2.4
      - if: env.RELEASES_CREATED == 'true'
        run: |
          uv python install
          uv run mike deploy --push --update-aliases "${{ env.TAG }}" latest -F mkdocs.yml
          uv run mike set-default --push latest -F mkdocs.yml
      - if: env.RELEASES_CREATED == 'false'
        run: |
          uv python install
          uv run mike deploy --push --update-aliases edge -F mkdocs.yml
      - run: |
          uv run mike list
